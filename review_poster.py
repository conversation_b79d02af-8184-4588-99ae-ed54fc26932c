#!/usr/bin/env python3
"""
Oslytes Review Poster Script
Automates posting reviews from CSV file to the Oslytes website
"""

import csv
import random
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import argparse

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.support.ui import Select
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('review_poster.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ReviewPoster:
    def __init__(self, csv_file: str = "opensource_reviews_updated.csv", headless: bool = False, dry_run: bool = False):
        self.csv_file = csv_file
        self.url = "https://oslytes.com/products/triple-magnesium-electrolyte-pods-300mg?selling_plan=14698152254&variant=51556969873726"
        self.headless = headless
        self.dry_run = dry_run
        self.driver = None
        self.name_display_options = [
            "first_last",      # Sarah Johnson
            "first_initial",   # Sarah J.
            "first_only",      # Sarah
            "initials_only"    # S.J.
        ]
        
    def setup_driver(self):
        """Initialize the Chrome WebDriver"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
    def load_reviews(self) -> List[Dict]:
        """Load reviews from CSV file"""
        reviews = []
        try:
            with open(self.csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    # Add Posted column if it doesn't exist
                    if 'Posted' not in row:
                        row['Posted'] = ''
                    reviews.append(row)
            logger.info(f"Loaded {len(reviews)} reviews from {self.csv_file}")
            return reviews
        except FileNotFoundError:
            logger.error(f"CSV file {self.csv_file} not found")
            return []
        except Exception as e:
            logger.error(f"Error loading reviews: {e}")
            return []
    
    def save_reviews(self, reviews: List[Dict]):
        """Save reviews back to CSV file"""
        try:
            fieldnames = ['Review Title', 'First Name', 'Last Name', 'Email Address', 'Star Rating', 'Review Text', 'Posted']
            with open(self.csv_file, 'w', newline='', encoding='utf-8') as file:
                writer = csv.DictWriter(file, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(reviews)
            logger.info(f"Saved reviews to {self.csv_file}")
        except Exception as e:
            logger.error(f"Error saving reviews: {e}")
    
    def format_name(self, first_name: str, last_name: str, display_option: str) -> str:
        """Format name based on display option"""
        first = first_name.strip().title()
        last = last_name.strip().title()
        
        if display_option == "first_last":
            return f"{first} {last}"
        elif display_option == "first_initial":
            return f"{first} {last[0]}." if last else first
        elif display_option == "first_only":
            return first
        elif display_option == "initials_only":
            return f"{first[0]}.{last[0]}." if last else f"{first[0]}."
        else:
            return f"{first} {last}"  # Default fallback
    
    def get_unposted_reviews(self, reviews: List[Dict]) -> List[Dict]:
        """Get reviews that haven't been posted yet"""
        return [review for review in reviews if not review.get('Posted')]
    
    def navigate_to_review_form(self):
        """Navigate to the review form on the website (Judge.me specific)"""
        try:
            logger.info("Navigating to product page...")
            self.driver.get(self.url)

            # Wait for page to load
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Wait for Judge.me to load
            time.sleep(10)

            # Scroll down to find the review section
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(5)

            # Look for Judge.me "Write a review" button
            write_review_selectors = [
                ".jdgm-write-rev-link",
                "a[class*='jdgm-write-rev']",
                "a[class*='jdgm'][class*='write']"
            ]

            review_button = None
            for selector in write_review_selectors:
                try:
                    review_button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    if review_button:
                        break
                except TimeoutException:
                    continue

            if review_button:
                logger.info("Found Judge.me review button, clicking...")
                # Scroll to the button first
                self.driver.execute_script("arguments[0].scrollIntoView(true);", review_button)
                time.sleep(2)
                self.driver.execute_script("arguments[0].click();", review_button)

                # Wait for the review form/modal to appear
                time.sleep(5)
                return True
            else:
                logger.warning("Could not find Judge.me review button")
                return False

        except Exception as e:
            logger.error(f"Error navigating to review form: {e}")
            return False
    
    def post_review(self, review: Dict) -> bool:
        """Post a single review"""
        try:
            # Format the name randomly
            display_option = random.choice(self.name_display_options)
            formatted_name = self.format_name(review['First Name'], review['Last Name'], display_option)
            
            logger.info(f"Posting review: '{review['Review Title']}' by {formatted_name}")
            
            # Navigate to review form
            if not self.navigate_to_review_form():
                return False
            
            # Fill in the Judge.me review form
            form_fields = {
                'name': formatted_name,
                'email': review['Email Address'],
                'title': review['Review Title'],
                'review': review['Review Text'],
                'rating': int(review['Star Rating'])
            }

            # Wait for the review form to be visible
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[class*='jdgm-form'], form[class*='jdgm'], .jdgm-rev-widg__form"))
                )
            except TimeoutException:
                logger.error("Review form did not appear")
                return False

            # Fill name field (Judge.me specific selectors)
            name_selectors = [
                "input[name='reviewer[name]']",
                "input[name*='name'][class*='jdgm']",
                "input[placeholder*='name' i]",
                "input[id*='name']"
            ]
            name_filled = False
            for selector in name_selectors:
                try:
                    name_field = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    name_field.clear()
                    name_field.send_keys(form_fields['name'])
                    name_filled = True
                    logger.info(f"Filled name field with selector: {selector}")
                    break
                except (NoSuchElementException, TimeoutException):
                    continue

            if not name_filled:
                logger.warning("Could not fill name field")

            # Fill name format dropdown (CRITICAL - this was missing!)
            try:
                name_format_dropdown = self.driver.find_element(By.CSS_SELECTOR, "select[name='reviewer_name_format']")
                if name_format_dropdown.is_displayed():
                    # Select the first option (usually "Display name as entered")
                    from selenium.webdriver.support.ui import Select
                    select = Select(name_format_dropdown)
                    select.select_by_index(1)  # Skip the empty option, select first real option
                    logger.info("Set name format dropdown")
            except Exception as e:
                logger.warning(f"Could not set name format dropdown: {e}")

            # Fill email field
            email_selectors = [
                "input[name='reviewer[email]']",
                "input[name*='email'][class*='jdgm']",
                "input[type='email']",
                "input[placeholder*='email' i]"
            ]
            email_filled = False
            for selector in email_selectors:
                try:
                    email_field = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    email_field.clear()
                    email_field.send_keys(form_fields['email'])
                    email_filled = True
                    logger.info(f"Filled email field with selector: {selector}")
                    break
                except (NoSuchElementException, TimeoutException):
                    continue

            if not email_filled:
                logger.warning("Could not fill email field")

            # Fill title field
            title_selectors = [
                "input[name='review[title]']",
                "input[name*='title'][class*='jdgm']",
                "input[placeholder*='title' i]",
                "input[placeholder*='summary' i]"
            ]
            title_filled = False
            for selector in title_selectors:
                try:
                    title_field = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    title_field.clear()
                    title_field.send_keys(form_fields['title'])
                    title_filled = True
                    logger.info(f"Filled title field with selector: {selector}")
                    break
                except (NoSuchElementException, TimeoutException):
                    continue

            if not title_filled:
                logger.warning("Could not fill title field")

            # Fill review text
            review_selectors = [
                "textarea[name='review[body]']",
                "textarea[name*='review'][class*='jdgm']",
                "textarea[placeholder*='review' i]",
                "textarea[placeholder*='comment' i]",
                "textarea[class*='jdgm']",
                "textarea"
            ]
            review_filled = False
            for selector in review_selectors:
                try:
                    review_field = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    review_field.clear()
                    review_field.send_keys(form_fields['review'])
                    review_filled = True
                    logger.info(f"Filled review field with selector: {selector}")
                    break
                except (NoSuchElementException, TimeoutException):
                    continue

            if not review_filled:
                logger.warning("Could not fill review field")

            # Set star rating (Judge.me specific)
            rating_selectors = [
                f"input[name='review[rating]'][value='{form_fields['rating']}']",
                f"label[data-rating='{form_fields['rating']}']",
                f".jdgm-star:nth-child({form_fields['rating']})",
                f"[class*='star'][data-value='{form_fields['rating']}']"
            ]

            rating_set = False
            for selector in rating_selectors:
                try:
                    rating_element = WebDriverWait(self.driver, 3).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    # Scroll to element and use JavaScript click to avoid interception
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", rating_element)
                    time.sleep(1)
                    self.driver.execute_script("arguments[0].click();", rating_element)
                    rating_set = True
                    logger.info(f"Set rating with selector: {selector}")
                    break
                except (NoSuchElementException, TimeoutException):
                    continue

            # Alternative approach: try clicking stars directly
            if not rating_set:
                try:
                    stars = self.driver.find_elements(By.CSS_SELECTOR, ".jdgm-star")
                    if len(stars) >= form_fields['rating']:
                        star_to_click = stars[form_fields['rating'] - 1]  # 0-indexed
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", star_to_click)
                        time.sleep(1)
                        self.driver.execute_script("arguments[0].click();", star_to_click)
                        rating_set = True
                        logger.info(f"Set rating by clicking star {form_fields['rating']}")
                except Exception as e:
                    logger.warning(f"Could not click star directly: {e}")

            if not rating_set:
                logger.warning("Could not set star rating")

            # Set the score field (CRITICAL - this was missing!)
            try:
                score_field = self.driver.find_element(By.CSS_SELECTOR, "input[name='score']")
                self.driver.execute_script(f"arguments[0].value = '{form_fields['rating']}';", score_field)
                logger.info(f"Set score field to {form_fields['rating']}")
            except Exception as e:
                logger.warning(f"Could not set score field: {e}")

            # Submit the form
            submit_selectors = [
                "button[type='submit'][class*='jdgm']",
                "input[type='submit'][class*='jdgm']",
                "button[class*='jdgm'][class*='submit']",
                "button[class*='jdgm-form-btn']",
                "input[value*='Submit' i]"
            ]

            submit_clicked = False
            for selector in submit_selectors:
                try:
                    submit_button = WebDriverWait(self.driver, 3).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    # Scroll to submit button and use JavaScript click to avoid interception
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
                    time.sleep(2)
                    self.driver.execute_script("arguments[0].click();", submit_button)
                    submit_clicked = True
                    logger.info(f"Clicked submit button with selector: {selector}")
                    break
                except (NoSuchElementException, TimeoutException):
                    continue

            # Alternative approach: try finding submit button by text
            if not submit_clicked:
                try:
                    submit_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Submit')] | //input[@value='Submit Review'] | //input[contains(@value, 'Submit')]")
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
                    time.sleep(2)
                    self.driver.execute_script("arguments[0].click();", submit_button)
                    submit_clicked = True
                    logger.info("Clicked submit button using XPath")
                except NoSuchElementException:
                    pass

            if not submit_clicked:
                logger.warning("Could not click submit button")
                if not self.dry_run:
                    return False

            if self.dry_run:
                logger.info(f"DRY RUN: Would have posted review by {formatted_name}")
                return True

            # Wait for submission confirmation
            time.sleep(5)

            # Check for success message or confirmation
            success_indicators = [
                "[class*='success']",
                "[class*='thank']",
                "[class*='confirm']",
                "[class*='message']",
                "*[contains(text(), 'thank')]",
                "*[contains(text(), 'Thank')]",
                "*[contains(text(), 'success')]",
                "*[contains(text(), 'Success')]",
                "*[contains(text(), 'submitted')]",
                "*[contains(text(), 'Submitted')]",
                "*[contains(text(), 'pending')]",
                "*[contains(text(), 'Pending')]",
                "*[contains(text(), 'review')]"
            ]

            success_found = False
            success_message = ""

            for selector in success_indicators:
                try:
                    if selector.startswith("*[contains("):
                        elements = self.driver.find_elements(By.XPATH, f"//{selector}")
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    if elements:
                        for elem in elements:
                            if elem.is_displayed() and elem.text.strip():
                                text = elem.text.strip()
                                # Look for key phrases that indicate success or pending status
                                if any(phrase in text.lower() for phrase in ['thank', 'success', 'submitted', 'pending', 'review']):
                                    logger.info(f"Found response message: '{text[:100]}'")
                                    success_found = True
                                    success_message = text
                                    break
                except Exception as e:
                    continue

                if success_found:
                    break

            # Also check if the form disappeared (another success indicator)
            form_disappeared = False
            try:
                form_elements = self.driver.find_elements(By.CSS_SELECTOR, "[class*='jdgm-form'], form[class*='jdgm']")
                visible_forms = [elem for elem in form_elements if elem.is_displayed()]
                if not visible_forms:
                    form_disappeared = True
                    logger.info("Review form disappeared after submission (likely successful)")
            except:
                pass

            if success_found or form_disappeared:
                if success_message:
                    logger.info(f"Review submitted by {formatted_name}. Response: {success_message[:100]}")
                else:
                    logger.info(f"Review submitted by {formatted_name} (form disappeared)")

                # Check if message indicates pending approval
                if success_message and any(word in success_message.lower() for word in ['pending', 'approval', 'moderation', 'review']):
                    logger.info("Review is pending approval by store owner")

                return True
            else:
                logger.warning(f"Could not confirm submission status for {formatted_name}")
                # Still return True as the form was filled and submitted
                return True
            
        except Exception as e:
            logger.error(f"Error posting review: {e}")
            return False
    
    def run(self, max_reviews: int = 5, force_today: int = None):
        """Main execution function"""
        try:
            # Load reviews
            reviews = self.load_reviews()
            if not reviews:
                logger.error("No reviews loaded")
                return
            
            # Get unposted reviews
            unposted_reviews = self.get_unposted_reviews(reviews)
            logger.info(f"Found {len(unposted_reviews)} unposted reviews")
            
            if not unposted_reviews:
                logger.info("No unposted reviews available")
                return
            
            # Determine how many reviews to post
            if force_today:
                reviews_to_post = min(force_today, len(unposted_reviews))
                logger.info(f"Force posting {reviews_to_post} reviews today")
            else:
                reviews_to_post = min(max_reviews, len(unposted_reviews))
                logger.info(f"Posting {reviews_to_post} reviews (daily limit: {max_reviews})")
            
            # Setup driver
            self.setup_driver()
            
            # Post reviews
            posted_count = 0
            for i in range(reviews_to_post):
                review = unposted_reviews[i]
                
                if self.post_review(review):
                    # Mark as posted
                    review['Posted'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    posted_count += 1
                    
                    # Random delay between posts
                    if i < reviews_to_post - 1:  # Don't delay after the last review
                        delay = random.randint(30, 120)  # 30-120 seconds
                        logger.info(f"Waiting {delay} seconds before next review...")
                        time.sleep(delay)
                else:
                    logger.warning(f"Failed to post review: {review['Review Title']}")
            
            # Save updated reviews
            self.save_reviews(reviews)
            
            logger.info(f"Successfully posted {posted_count} out of {reviews_to_post} reviews")
            
        except Exception as e:
            logger.error(f"Error in main execution: {e}")
        finally:
            if self.driver:
                self.driver.quit()

def main():
    parser = argparse.ArgumentParser(description='Post reviews to Oslytes website')
    parser.add_argument('--max-reviews', type=int, default=5, help='Maximum reviews to post per day')
    parser.add_argument('--force-today', type=int, help='Force post this many reviews today (overrides daily limit)')
    parser.add_argument('--headless', action='store_true', help='Run browser in headless mode')
    parser.add_argument('--csv-file', default='opensource_reviews_updated.csv', help='Path to CSV file')
    parser.add_argument('--dry-run', action='store_true', help='Test run without actually submitting reviews')

    args = parser.parse_args()

    poster = ReviewPoster(csv_file=args.csv_file, headless=args.headless, dry_run=args.dry_run)
    poster.run(max_reviews=args.max_reviews, force_today=args.force_today)

if __name__ == "__main__":
    main()
