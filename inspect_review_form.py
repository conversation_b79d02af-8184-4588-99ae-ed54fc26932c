#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to inspect the Oslytes review form structure
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time

def inspect_review_form():
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--user-data-dir=/tmp/chrome_user_data")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        url = "https://oslytes.com/products/triple-magnesium-electrolyte-pods-300mg?selling_plan=14698152254&variant=51556969873726"
        print(f"Navigating to: {url}")
        driver.get(url)
        
        # Wait for page to load
        time.sleep(5)
        
        # Scroll down to find review section
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)
        
        # Look for review-related elements
        print("\n=== Looking for review elements ===")
        
        # Check for review buttons/links
        review_keywords = ['review', 'Review', 'REVIEW']
        for keyword in review_keywords:
            elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{keyword}')]")
            if elements:
                print(f"\nFound elements containing '{keyword}':")
                for i, elem in enumerate(elements[:5]):  # Limit to first 5
                    try:
                        print(f"  {i+1}. Tag: {elem.tag_name}, Text: '{elem.text[:50]}...', Class: {elem.get_attribute('class')}")
                    except:
                        print(f"  {i+1}. Could not get element details")
        
        # Look for form elements
        print("\n=== Looking for form elements ===")
        forms = driver.find_elements(By.TAG_NAME, "form")
        print(f"Found {len(forms)} form(s)")
        
        for i, form in enumerate(forms):
            print(f"\nForm {i+1}:")
            try:
                print(f"  Action: {form.get_attribute('action')}")
                print(f"  Method: {form.get_attribute('method')}")
                print(f"  Class: {form.get_attribute('class')}")
                
                # Find inputs in this form
                inputs = form.find_elements(By.TAG_NAME, "input")
                textareas = form.find_elements(By.TAG_NAME, "textarea")
                selects = form.find_elements(By.TAG_NAME, "select")
                
                print(f"  Inputs: {len(inputs)}")
                for inp in inputs[:3]:  # First 3 inputs
                    print(f"    - Type: {inp.get_attribute('type')}, Name: {inp.get_attribute('name')}, Placeholder: {inp.get_attribute('placeholder')}")
                
                print(f"  Textareas: {len(textareas)}")
                for ta in textareas:
                    print(f"    - Name: {ta.get_attribute('name')}, Placeholder: {ta.get_attribute('placeholder')}")
                
                print(f"  Selects: {len(selects)}")
                for sel in selects:
                    print(f"    - Name: {sel.get_attribute('name')}")
                    
            except Exception as e:
                print(f"  Error inspecting form: {e}")
        
        # Look for iframes (review widgets are often in iframes)
        print("\n=== Looking for iframes ===")
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        print(f"Found {len(iframes)} iframe(s)")
        
        for i, iframe in enumerate(iframes):
            try:
                src = iframe.get_attribute('src')
                print(f"  Iframe {i+1}: {src}")
                if 'review' in src.lower() or 'rating' in src.lower():
                    print(f"    *** This might be a review iframe! ***")
            except:
                print(f"  Iframe {i+1}: Could not get src")
        
        # Check page source for review-related content
        print("\n=== Checking page source for review patterns ===")
        page_source = driver.page_source.lower()
        
        review_patterns = [
            'write a review',
            'submit review',
            'review form',
            'rating',
            'stars',
            'customer review'
        ]
        
        for pattern in review_patterns:
            if pattern in page_source:
                print(f"  Found pattern: '{pattern}'")
        
        # Take a screenshot for manual inspection
        driver.save_screenshot("review_page_screenshot.png")
        print("\nScreenshot saved as 'review_page_screenshot.png'")
        
        # Print final summary
        print("\nInspection complete!")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    inspect_review_form()
