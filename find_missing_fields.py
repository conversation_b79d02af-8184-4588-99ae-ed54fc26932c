#!/usr/bin/env python3
"""
Find missing required fields in Judge.me form
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

def find_missing_fields():
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--user-data-dir=/tmp/chrome_fields_" + str(int(time.time())))
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        url = "https://oslytes.com/products/triple-magnesium-electrolyte-pods-300mg?selling_plan=14698152254&variant=51556969873726"
        print(f"Navigating to: {url}")
        driver.get(url)
        
        WebDriverWait(driver, 15).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        time.sleep(10)
        
        # Click review button
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(5)
        
        review_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, ".jdgm-write-rev-link"))
        )
        driver.execute_script("arguments[0].scrollIntoView(true);", review_button)
        time.sleep(2)
        driver.execute_script("arguments[0].click();", review_button)
        
        time.sleep(10)
        
        print("\n=== ANALYZING ALL FORM FIELDS ===")
        
        # Find all input fields in the form
        all_inputs = driver.find_elements(By.CSS_SELECTOR, "input, textarea, select")
        
        print(f"Found {len(all_inputs)} total form elements")
        
        for i, element in enumerate(all_inputs):
            try:
                tag = element.tag_name
                input_type = element.get_attribute('type') or 'text'
                placeholder = element.get_attribute('placeholder') or ''
                name = element.get_attribute('name') or ''
                id_attr = element.get_attribute('id') or ''
                classes = element.get_attribute('class') or ''
                required = element.get_attribute('required') or ''
                value = element.get_attribute('value') or ''
                visible = element.is_displayed()
                
                print(f"\n{i+1}. {tag.upper()} element:")
                print(f"   Type: {input_type}")
                print(f"   Placeholder: '{placeholder}'")
                print(f"   Name: '{name}'")
                print(f"   ID: '{id_attr}'")
                print(f"   Classes: '{classes}'")
                print(f"   Required: {bool(required)}")
                print(f"   Current value: '{value}'")
                print(f"   Visible: {visible}")
                
                # Check if this looks like a field we should fill
                if visible and (required or 'required' in classes.lower()):
                    print(f"   ⚠️  REQUIRED FIELD!")
                
            except Exception as e:
                print(f"   Error analyzing element {i+1}: {e}")
        
        # Also check for any hidden required fields
        print("\n=== CHECKING FOR HIDDEN REQUIRED FIELDS ===")
        hidden_required = driver.find_elements(By.CSS_SELECTOR, "input[required]:not([type='submit']), textarea[required], select[required]")
        
        for element in hidden_required:
            try:
                visible = element.is_displayed()
                if not visible:
                    placeholder = element.get_attribute('placeholder') or ''
                    name = element.get_attribute('name') or ''
                    classes = element.get_attribute('class') or ''
                    print(f"Hidden required field: {element.tag_name} - name:'{name}' placeholder:'{placeholder}' classes:'{classes}'")
            except:
                continue
        
        # Try to submit empty form to see what validation messages appear
        print("\n=== TESTING EMPTY FORM SUBMISSION ===")
        try:
            submit_button = driver.find_element(By.CSS_SELECTOR, "input[type='submit'][class*='jdgm']")
            driver.execute_script("arguments[0].click();", submit_button)
            time.sleep(5)
            
            # Look for validation messages
            validation_selectors = [
                "[class*='error']",
                "[class*='invalid']", 
                "[class*='required']",
                "*[contains(text(), 'required')]",
                "*[contains(text(), 'field')]"
            ]
            
            print("Validation messages after empty submission:")
            for selector in validation_selectors:
                try:
                    if selector.startswith("*[contains("):
                        elements = driver.find_elements(By.XPATH, f"//{selector}")
                    else:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for elem in elements:
                        if elem.is_displayed() and elem.text.strip():
                            print(f"  - {elem.text.strip()}")
                except:
                    continue
                    
        except Exception as e:
            print(f"Error testing empty submission: {e}")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        driver.quit()

if __name__ == "__main__":
    find_missing_fields()
