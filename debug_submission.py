#!/usr/bin/env python3
"""
Debug script to investigate review submission and approval process
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import time

def debug_review_submission():
    # Setup Chrome driver (visible mode for debugging)
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--user-data-dir=/tmp/chrome_debug")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        url = "https://oslytes.com/products/triple-magnesium-electrolyte-pods-300mg?selling_plan=14698152254&variant=51556969873726"
        print(f"Navigating to: {url}")
        driver.get(url)
        
        # Wait for page to load
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Wait for Judge.me to load
        print("Waiting for Judge.me to load...")
        time.sleep(10)
        
        # Check current review count
        print("\n=== Checking current review count ===")
        review_count_selectors = [
            ".jdgm-prev-badge__text",
            "[class*='jdgm'][class*='review']",
            "[class*='review-count']"
        ]
        
        for selector in review_count_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for elem in elements:
                    if elem.is_displayed() and elem.text.strip():
                        print(f"Found review info: '{elem.text}' (selector: {selector})")
            except:
                continue
        
        # Scroll down to find the review section
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(5)
        
        # Look for existing reviews
        print("\n=== Looking for existing reviews ===")
        existing_review_selectors = [
            "[class*='jdgm-rev']",
            "[class*='review-item']",
            ".jdgm-rev-widg__reviews"
        ]
        
        for selector in existing_review_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"Found {len(elements)} elements with selector: {selector}")
                    for i, elem in enumerate(elements[:3]):
                        if elem.is_displayed():
                            text = elem.text[:100].replace('\n', ' ')
                            print(f"  {i+1}. {text}...")
            except:
                continue
        
        # Click write review button
        print("\n=== Clicking write review button ===")
        try:
            review_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, ".jdgm-write-rev-link"))
            )
            driver.execute_script("arguments[0].scrollIntoView(true);", review_button)
            time.sleep(2)
            driver.execute_script("arguments[0].click();", review_button)
            print("Clicked review button")
        except Exception as e:
            print(f"Could not click review button: {e}")
            return
        
        # Wait for form to appear
        time.sleep(10)
        
        # Fill out a test review
        print("\n=== Filling test review ===")
        
        # Fill name
        try:
            name_field = driver.find_element(By.CSS_SELECTOR, "input[placeholder*='name' i]")
            name_field.clear()
            name_field.send_keys("Test User")
            print("Filled name field")
        except Exception as e:
            print(f"Could not fill name: {e}")
        
        # Fill email
        try:
            email_field = driver.find_element(By.CSS_SELECTOR, "input[type='email']")
            email_field.clear()
            email_field.send_keys("<EMAIL>")
            print("Filled email field")
        except Exception as e:
            print(f"Could not fill email: {e}")
        
        # Fill title
        try:
            title_field = driver.find_element(By.CSS_SELECTOR, "input[placeholder*='title' i]")
            title_field.clear()
            title_field.send_keys("Debug Test Review")
            print("Filled title field")
        except Exception as e:
            print(f"Could not fill title: {e}")
        
        # Fill review text
        try:
            review_field = driver.find_element(By.CSS_SELECTOR, "textarea")
            review_field.clear()
            review_field.send_keys("This is a debug test review to check the submission process.")
            print("Filled review field")
        except Exception as e:
            print(f"Could not fill review: {e}")
        
        # Set rating
        try:
            stars = driver.find_elements(By.CSS_SELECTOR, ".jdgm-star")
            if len(stars) >= 5:
                star_to_click = stars[4]  # 5th star (0-indexed)
                driver.execute_script("arguments[0].click();", star_to_click)
                print("Set 5-star rating")
        except Exception as e:
            print(f"Could not set rating: {e}")
        
        # Submit the form
        print("\n=== Submitting form ===")
        try:
            submit_button = driver.find_element(By.CSS_SELECTOR, "input[type='submit'][class*='jdgm']")
            driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
            time.sleep(2)
            driver.execute_script("arguments[0].click();", submit_button)
            print("Clicked submit button")
        except Exception as e:
            print(f"Could not click submit: {e}")
            return
        
        # Wait and check for response
        print("\n=== Checking submission response ===")
        time.sleep(10)
        
        # Look for success messages, error messages, or confirmation
        response_selectors = [
            "[class*='success']",
            "[class*='error']",
            "[class*='thank']",
            "[class*='confirm']",
            "[class*='message']",
            "[class*='alert']",
            "[class*='notification']"
        ]
        
        found_response = False
        for selector in response_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for elem in elements:
                    if elem.is_displayed() and elem.text.strip():
                        print(f"Found response: '{elem.text}' (selector: {selector})")
                        found_response = True
            except:
                continue
        
        if not found_response:
            print("No clear success/error message found")
        
        # Check if form is still visible or disappeared
        try:
            form_still_visible = driver.find_element(By.CSS_SELECTOR, "[class*='jdgm-form']").is_displayed()
            print(f"Form still visible: {form_still_visible}")
        except:
            print("Form no longer visible")
        
        # Check page source for any Judge.me related messages
        print("\n=== Checking page source for Judge.me messages ===")
        page_source = driver.page_source.lower()
        
        judge_messages = [
            "thank you",
            "review submitted",
            "pending approval",
            "moderation",
            "published",
            "approved"
        ]
        
        for message in judge_messages:
            if message in page_source:
                # Find the context around the message
                start = page_source.find(message)
                context = page_source[max(0, start-100):start+100]
                print(f"Found '{message}' in page source: ...{context}...")
        
        # Take a screenshot for manual inspection
        driver.save_screenshot("debug_submission.png")
        print("\nScreenshot saved as 'debug_submission.png'")
        
        # Keep browser open for manual inspection
        input("\nPress Enter to close the browser and continue...")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_review_submission()
