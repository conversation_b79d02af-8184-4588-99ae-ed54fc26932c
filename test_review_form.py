#!/usr/bin/env python3
"""
Test script to verify Judge.me review form access
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import time

def test_review_form_access():
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--user-data-dir=/tmp/chrome_user_data_test")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        url = "https://oslytes.com/products/triple-magnesium-electrolyte-pods-300mg?selling_plan=14698152254&variant=51556969873726"
        print(f"Navigating to: {url}")
        driver.get(url)
        
        # Wait for page to load
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Wait for Judge.me to load
        print("Waiting for Judge.me to load...")
        time.sleep(10)
        
        # Scroll down to find the review section
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(5)
        
        # Look for Judge.me "Write a review" button
        print("Looking for 'Write a review' button...")
        write_review_selectors = [
            ".jdgm-write-rev-link",
            "a[class*='jdgm-write-rev']",
            "a[class*='jdgm'][class*='write']"
        ]
        
        review_button = None
        for selector in write_review_selectors:
            try:
                review_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                )
                if review_button:
                    print(f"Found review button with selector: {selector}")
                    print(f"Button text: '{review_button.text}'")
                    print(f"Button href: {review_button.get_attribute('href')}")
                    break
            except TimeoutException:
                continue
        
        if not review_button:
            print("Could not find review button")
            return False
        
        # Click the review button
        print("Clicking review button...")
        driver.execute_script("arguments[0].scrollIntoView(true);", review_button)
        time.sleep(2)
        driver.execute_script("arguments[0].click();", review_button)
        
        # Wait for the review form/modal to appear
        print("Waiting for review form to appear...")
        time.sleep(10)
        
        # Look for form elements
        print("Looking for form elements...")
        
        # Check for form container
        form_containers = [
            "[class*='jdgm-form']",
            "form[class*='jdgm']",
            ".jdgm-rev-widg__form",
            "[class*='jdgm'][class*='modal']",
            "[class*='jdgm'][class*='popup']"
        ]
        
        form_found = False
        for selector in form_containers:
            try:
                form = driver.find_element(By.CSS_SELECTOR, selector)
                if form.is_displayed():
                    print(f"Found visible form with selector: {selector}")
                    form_found = True
                    break
                else:
                    print(f"Found hidden form with selector: {selector}")
            except NoSuchElementException:
                continue
        
        if not form_found:
            print("No form container found")
        
        # Look for specific form fields
        field_selectors = {
            'name': [
                "input[name='reviewer[name]']",
                "input[name*='name'][class*='jdgm']",
                "input[placeholder*='name' i]"
            ],
            'email': [
                "input[name='reviewer[email]']",
                "input[name*='email'][class*='jdgm']",
                "input[type='email']"
            ],
            'title': [
                "input[name='review[title]']",
                "input[name*='title'][class*='jdgm']",
                "input[placeholder*='title' i]"
            ],
            'review': [
                "textarea[name='review[body]']",
                "textarea[name*='review'][class*='jdgm']",
                "textarea[placeholder*='review' i]",
                "textarea[placeholder*='comment' i]",
                "textarea[class*='jdgm']",
                "textarea"
            ],
            'rating': [
                "input[name='review[rating]']",
                "[class*='star'][class*='jdgm']",
                "[data-rating]"
            ]
        }
        
        for field_name, selectors in field_selectors.items():
            field_found = False
            for selector in selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        visible_elements = [elem for elem in elements if elem.is_displayed()]
                        if visible_elements:
                            print(f"Found visible {field_name} field with selector: {selector} ({len(visible_elements)} elements)")
                            field_found = True
                            break
                        else:
                            print(f"Found hidden {field_name} field with selector: {selector} ({len(elements)} elements)")
                except Exception as e:
                    continue
            
            if not field_found:
                print(f"No {field_name} field found")
        
        # Take a screenshot for manual inspection
        driver.save_screenshot("review_form_test.png")
        print("Screenshot saved as 'review_form_test.png'")
        
        # Print completion message
        print("\nTest completed!")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        driver.quit()

if __name__ == "__main__":
    test_review_form_access()
