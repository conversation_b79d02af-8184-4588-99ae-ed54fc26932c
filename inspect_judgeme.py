#!/usr/bin/env python3
"""
Script to inspect Judge.me review widget structure
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

def inspect_judgeme_widget():
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--user-data-dir=/tmp/chrome_user_data")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        url = "https://oslytes.com/products/triple-magnesium-electrolyte-pods-300mg?selling_plan=14698152254&variant=51556969873726"
        print(f"Navigating to: {url}")
        driver.get(url)
        
        # Wait for page to load
        time.sleep(10)
        
        # Scroll down to find review section
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(5)
        
        print("\n=== Looking for Judge.me elements ===")
        
        # Look for Judge.me specific elements
        jdgm_elements = driver.find_elements(By.CSS_SELECTOR, "[class*='jdgm']")
        print(f"Found {len(jdgm_elements)} Judge.me elements")
        
        for i, elem in enumerate(jdgm_elements[:10]):  # First 10 elements
            try:
                print(f"  {i+1}. Tag: {elem.tag_name}, Class: {elem.get_attribute('class')}, Text: '{elem.text[:50]}...'")
            except:
                print(f"  {i+1}. Could not get element details")
        
        # Look for "Write a review" button specifically
        print("\n=== Looking for 'Write a review' button ===")
        write_review_selectors = [
            "[class*='jdgm'][class*='write']",
            "[class*='jdgm'][class*='btn']",
            "a[href*='review']",
            "button[class*='review']",
            "*[text()='Write a review']",
            "*[text()='Write Review']"
        ]
        
        for selector in write_review_selectors:
            try:
                if selector.startswith("*[text()"):
                    elements = driver.find_elements(By.XPATH, f"//{selector}")
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                
                if elements:
                    print(f"  Found {len(elements)} elements with selector: {selector}")
                    for elem in elements[:3]:
                        try:
                            print(f"    - Tag: {elem.tag_name}, Class: {elem.get_attribute('class')}, Text: '{elem.text}'")
                            print(f"      Href: {elem.get_attribute('href')}")
                            print(f"      ID: {elem.get_attribute('id')}")
                        except:
                            print(f"    - Could not get element details")
            except Exception as e:
                print(f"  Error with selector {selector}: {e}")
        
        # Check if Judge.me widget is loaded via JavaScript
        print("\n=== Checking for Judge.me JavaScript ===")
        scripts = driver.find_elements(By.TAG_NAME, "script")
        for script in scripts:
            try:
                src = script.get_attribute('src')
                if src and 'judge' in src.lower():
                    print(f"  Found Judge.me script: {src}")
                
                content = script.get_attribute('innerHTML')
                if content and 'judge' in content.lower():
                    print(f"  Found Judge.me inline script (first 100 chars): {content[:100]}...")
            except:
                continue
        
        # Look for data attributes that might indicate Judge.me configuration
        print("\n=== Looking for Judge.me data attributes ===")
        elements_with_data = driver.find_elements(By.CSS_SELECTOR, "[data-*]")
        for elem in elements_with_data:
            try:
                attrs = driver.execute_script("return arguments[0].attributes;", elem)
                for attr in attrs:
                    if 'jdgm' in attr['name'].lower() or 'judge' in attr['name'].lower():
                        print(f"  Found data attribute: {attr['name']} = {attr['value']}")
            except:
                continue
        
        # Check page source for Judge.me configuration
        print("\n=== Checking page source for Judge.me config ===")
        page_source = driver.page_source
        
        # Look for Judge.me shop domain
        if 'jdgm-shop-domain' in page_source:
            start = page_source.find('jdgm-shop-domain')
            snippet = page_source[start:start+200]
            print(f"  Found shop domain config: {snippet}")
        
        # Look for product ID
        if 'jdgm-product-id' in page_source:
            start = page_source.find('jdgm-product-id')
            snippet = page_source[start:start+200]
            print(f"  Found product ID config: {snippet}")
        
        # Save page source for manual inspection
        with open('page_source.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        print("\nPage source saved as 'page_source.html'")
        
        print("\nInspection complete!")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    inspect_judgeme_widget()
