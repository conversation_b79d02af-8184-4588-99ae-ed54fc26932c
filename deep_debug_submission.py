#!/usr/bin/env python3
"""
Deep debug script to verify actual review submission
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import time
import json

def deep_debug_submission():
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--user-data-dir=/tmp/chrome_deep_debug_" + str(int(time.time())))
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        url = "https://oslytes.com/products/triple-magnesium-electrolyte-pods-300mg?selling_plan=14698152254&variant=51556969873726"
        print(f"Navigating to: {url}")
        driver.get(url)
        
        # Wait for page to load
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Wait for Judge.me to load
        print("Waiting for Judge.me to load...")
        time.sleep(10)
        
        # Check current review count BEFORE submission
        print("\n=== BEFORE SUBMISSION: Current review count ===")
        review_count_before = get_review_count(driver)
        print(f"Review count before: {review_count_before}")
        
        # Scroll and click review button
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(5)
        
        print("\n=== Clicking write review button ===")
        try:
            review_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, ".jdgm-write-rev-link"))
            )
            driver.execute_script("arguments[0].scrollIntoView(true);", review_button)
            time.sleep(2)
            driver.execute_script("arguments[0].click();", review_button)
            print("Clicked review button")
        except Exception as e:
            print(f"Could not click review button: {e}")
            return False
        
        # Wait for form to appear
        time.sleep(10)
        
        print("\n=== Filling and submitting review ===")

        # Fill form fields
        try:
            # Name
            name_field = driver.find_element(By.CSS_SELECTOR, "input[placeholder*='name' i]")
            name_field.clear()
            name_field.send_keys("Debug Test User")
            print("✓ Filled name")

            # Email
            email_field = driver.find_element(By.CSS_SELECTOR, "input[type='email']")
            email_field.clear()
            email_field.send_keys("<EMAIL>")
            print("✓ Filled email")

            # Title
            title_field = driver.find_element(By.CSS_SELECTOR, "input[placeholder*='title' i]")
            title_field.clear()
            title_field.send_keys("Deep Debug Test Review")
            print("✓ Filled title")

            # Review text
            review_field = driver.find_element(By.CSS_SELECTOR, "textarea")
            review_field.clear()
            review_field.send_keys("This is a deep debug test to verify the actual submission process.")
            print("✓ Filled review text")

            # Rating
            stars = driver.find_elements(By.CSS_SELECTOR, ".jdgm-star")
            if len(stars) >= 5:
                star_to_click = stars[4]  # 5th star
                driver.execute_script("arguments[0].click();", star_to_click)
                print("✓ Set 5-star rating")

        except Exception as e:
            print(f"Error filling form: {e}")
            return False

        # Submit the form
        print("\n=== Submitting form ===")
        try:
            submit_button = driver.find_element(By.CSS_SELECTOR, "input[type='submit'][class*='jdgm']")
            driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
            time.sleep(2)

            # Get page source before submission for comparison
            page_source_before = driver.page_source

            # Click submit
            driver.execute_script("arguments[0].click();", submit_button)
            print("✓ Clicked submit button")

            # Wait for response
            time.sleep(15)

            # Get page source after submission
            page_source_after = driver.page_source

            # Check if page content changed significantly
            if len(page_source_after) != len(page_source_before):
                print(f"✓ Page content changed (before: {len(page_source_before)} chars, after: {len(page_source_after)} chars)")
            else:
                print("⚠️  Page content unchanged after submission")

        except Exception as e:
            print(f"Error submitting form: {e}")
            return False
        
        # Look for success/error messages on page
        print("\n=== Checking page response ===")
        time.sleep(5)
        
        # Check for any visible messages
        message_selectors = [
            "[class*='success']",
            "[class*='error']", 
            "[class*='message']",
            "[class*='alert']",
            "[class*='notification']",
            "[class*='jdgm'][class*='message']",
            ".jdgm-form-success",
            ".jdgm-form-error"
        ]
        
        found_messages = []
        for selector in message_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for elem in elements:
                    if elem.is_displayed() and elem.text.strip():
                        found_messages.append(elem.text.strip())
            except:
                continue
        
        if found_messages:
            print("Found page messages:")
            for msg in found_messages:
                print(f"  - {msg[:100]}")
        else:
            print("No visible success/error messages found")
        
        # Check if form is still visible
        try:
            form_visible = driver.find_element(By.CSS_SELECTOR, "[class*='jdgm-form']").is_displayed()
            print(f"Form still visible after submission: {form_visible}")
        except:
            print("Form no longer visible (could indicate success)")
        
        # Wait a bit more and check review count AFTER submission
        print("\n=== AFTER SUBMISSION: Checking review count ===")
        time.sleep(10)
        
        # Refresh the page to get updated count
        driver.refresh()
        time.sleep(10)
        
        review_count_after = get_review_count(driver)
        print(f"Review count after: {review_count_after}")
        
        if review_count_after > review_count_before:
            print("✅ SUCCESS: Review count increased - review was published!")
            return True
        elif review_count_after == review_count_before:
            print("⚠️  Review count unchanged - review might be pending or failed")
            return False
        else:
            print("❓ Unexpected: Review count decreased")
            return False
        
    except Exception as e:
        print(f"Error in deep debug: {e}")
        return False
    finally:
        driver.quit()

def get_review_count(driver):
    """Extract current review count from the page"""
    count_selectors = [
        ".jdgm-prev-badge__text",
        "[class*='jdgm'][class*='review']",
        ".jdgm-rev-widg__summary-text"
    ]
    
    for selector in count_selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            for elem in elements:
                if elem.is_displayed():
                    text = elem.text.strip()
                    # Extract number from text like "14 reviews" or "5 stars"
                    import re
                    numbers = re.findall(r'\d+', text)
                    if numbers and 'review' in text.lower():
                        return int(numbers[0])
        except:
            continue
    
    return 0

if __name__ == "__main__":
    success = deep_debug_submission()
    print(f"\nFinal result: {'SUCCESS' if success else 'NEEDS INVESTIGATION'}")
