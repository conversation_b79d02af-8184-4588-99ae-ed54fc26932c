#!/usr/bin/env python3
"""
Test script to post a single review
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from review_poster import ReviewPoster
import logging

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO)

def test_single_review():
    # Create a test CSV with just one review
    test_csv_content = """Review Title,First Name,Last Name,Email Address,Star Rating,Review Text,Posted
"Test Review",<PERSON>,<PERSON>,<EMAIL>,5,"This is a test review to validate the automation script.","""
    
    with open('test_review.csv', 'w') as f:
        f.write(test_csv_content)
    
    # Create poster instance
    poster = ReviewPoster(csv_file='test_review.csv', headless=True)
    
    # Run with just 1 review
    poster.run(max_reviews=1)
    
    # Check if the review was marked as posted
    reviews = poster.load_reviews()
    if reviews and reviews[0].get('Posted'):
        print(f"SUCCESS: Review was posted at {reviews[0]['Posted']}")
        return True
    else:
        print("FAILED: Review was not marked as posted")
        return False

if __name__ == "__main__":
    test_single_review()
