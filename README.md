# Oslytes Review Automation Script

This script automates the posting of product reviews to the Oslytes website using Selenium WebDriver. It reads reviews from a CSV file and posts them through the Judge.me review widget.

## Features

- **Automated Review Posting**: Posts reviews to Oslytes product pages using browser automation
- **Judge.me Integration**: Specifically designed to work with Judge.me review widgets
- **CSV Tracking**: Tracks which reviews have been posted to avoid duplicates
- **Daily Limits**: Enforces posting limits (default: 5 reviews per day)
- **Name Randomization**: Randomly formats reviewer names (full name, first + initial, first only, initials only)
- **Dry Run Mode**: Test the script without actually submitting reviews
- **Error Handling**: Robust error handling with detailed logging
- **Headless Mode**: Can run without opening browser windows

## Requirements

- Python 3.7+
- Chrome browser installed
- Virtual environment (recommended)

## Installation

1. **Clone or download the project files**
2. **Create a virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```
3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

## CSV File Format

The script expects a CSV file with the following columns:
- `Review Title`: The title/subject of the review
- `First Name`: Reviewer's first name
- `Last Name`: Reviewer's last name  
- `Email Address`: Reviewer's email address
- `Star Rating`: Rating from 1-5 stars
- `Review Text`: The main review content
- `Posted`: Timestamp when review was posted (automatically filled)

Example:
```csv
Review Title,First Name,Last Name,Email Address,Star Rating,Review Text,Posted
"Great product!",John,Doe,<EMAIL>,5,"I love this product. Highly recommended!",
```

## Usage

### Basic Usage
```bash
# Post up to 5 reviews (default daily limit)
python review_poster.py

# Post up to 10 reviews
python review_poster.py --max-reviews 10

# Run in headless mode (no browser window)
python review_poster.py --headless

# Use a different CSV file
python review_poster.py --csv-file my_reviews.csv
```

### Testing
```bash
# Dry run - test without actually submitting reviews
python review_poster.py --dry-run --max-reviews 1

# Test with visible browser (for debugging)
python review_poster.py --dry-run --max-reviews 1
```

### Advanced Options
```bash
# Force post 20 reviews today (overrides daily limit)
python review_poster.py --force-today 20

# Combine options
python review_poster.py --headless --max-reviews 3 --csv-file custom_reviews.csv
```

## Command Line Options

- `--max-reviews N`: Maximum reviews to post per day (default: 5)
- `--force-today N`: Force post N reviews today, ignoring daily limits
- `--headless`: Run browser in headless mode (no visible window)
- `--csv-file PATH`: Path to CSV file (default: opensource_reviews_updated.csv)
- `--dry-run`: Test run without actually submitting reviews

## How It Works

1. **Load Reviews**: Reads unposted reviews from the CSV file
2. **Browser Setup**: Launches Chrome browser with appropriate settings
3. **Navigation**: Goes to the Oslytes product page
4. **Form Access**: Finds and clicks the Judge.me "Write a review" button
5. **Form Filling**: Fills in all review form fields:
   - Name (randomly formatted)
   - Email address
   - Review title
   - Review text
   - Star rating
6. **Submission**: Submits the review form
7. **Tracking**: Updates CSV file with posting timestamp
8. **Repeat**: Continues until daily limit is reached

## Name Display Options

The script randomly chooses from these name formats:
- **Full Name**: "Sarah Johnson"
- **First + Initial**: "Sarah J."
- **First Only**: "Sarah"
- **Initials Only**: "S.J."

## Logging

The script provides detailed logging including:
- Review loading and filtering
- Browser navigation steps
- Form field filling progress
- Success/failure status
- Error messages with details

## Troubleshooting

### Common Issues

1. **Chrome Driver Issues**: The script automatically downloads the correct ChromeDriver version
2. **Element Not Found**: The script uses multiple selectors to find form elements
3. **Click Intercepted**: Uses JavaScript clicks to avoid element interception
4. **Slow Loading**: Includes appropriate wait times for Judge.me widget loading

### Debug Mode
Run without `--headless` to see the browser in action:
```bash
python review_poster.py --dry-run --max-reviews 1
```

## Files

- `review_poster.py`: Main automation script
- `requirements.txt`: Python dependencies
- `opensource_reviews_updated.csv`: Sample CSV file with reviews
- `test_review_form.py`: Test script for form access
- `inspect_judgeme.py`: Inspection script for Judge.me elements

## Safety Features

- **Dry Run Mode**: Test without submitting
- **Daily Limits**: Prevents posting too many reviews at once
- **Duplicate Prevention**: Tracks posted reviews in CSV
- **Error Recovery**: Continues with next review if one fails
- **Detailed Logging**: Full audit trail of actions

## Support

For issues or questions:
1. Check the log output for error details
2. Try running with `--dry-run` first
3. Use visible browser mode (without `--headless`) for debugging
4. Ensure CSV file format matches requirements
